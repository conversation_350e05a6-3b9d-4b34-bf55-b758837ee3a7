import numpy as np
import math
from collections import deque



class ComponentWrapper:
    """将EasyEDA组件包装为类似force.py中PCBComponent的接口"""
    def __init__(self, name, fixed, width,height,position):
        self.id = name
        self.is_fixed = fixed
        self.width = width
        self.height = height
        self.position = np.array([position[0], position[1]], dtype=float)
        self.net_force = np.array([0.0, 0.0])
        self.velocity = np.array([0.0, 0.0])
        self.thermal_rating = 1.0  # 默认热评级

    def distance_to(self, other_component):
        return np.linalg.norm(self.position - other_component.position)

def is_point_in_polygon(point, polygon):
    """射线法判断点是否在多边形内部"""
    x, y = point
    n = len(polygon)
    inside = False

    p1x, p1y = polygon[0]
    for i in range(n + 1):
        p2x, p2y = polygon[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y

    return inside

class ForceDirectedLayout:
    def __init__(self, components, board_boundary=None):
        self.components = components
        self.board_boundary = board_boundary

        # 物理参数
        self.repulsion_strength = 100.0
        self.attraction_strength = 0.1
        self.damping = 0.9
        self.min_distance = 10.0
        self.time_step = 0.1

    def calculate_repulsion_force(self, comp1, comp2):
        """计算两个组件之间的排斥力"""
        if comp1 == comp2:
            return np.array([0.0, 0.0])

        distance_vec = comp1.position - comp2.position
        distance = np.linalg.norm(distance_vec)

        if distance < self.min_distance:
            distance = self.min_distance

        # 库仑定律：F = k * q1 * q2 / r^2
        force_magnitude = self.repulsion_strength / (distance ** 2)
        force_direction = distance_vec / distance

        return force_magnitude * force_direction

    def calculate_attraction_force(self, comp1, comp2):
        """计算连接组件之间的吸引力"""
        distance_vec = comp2.position - comp1.position
        distance = np.linalg.norm(distance_vec)

        # 胡克定律：F = k * x
        force_magnitude = self.attraction_strength * distance
        force_direction = distance_vec / distance if distance > 0 else np.array([0.0, 0.0])

        return force_magnitude * force_direction

    def apply_boundary_constraints(self, component):
        """应用边界约束"""
        if self.board_boundary is None:
            return

        # 简单的矩形边界约束
        if hasattr(self.board_boundary, '__len__') and len(self.board_boundary) == 4:
            min_x, min_y, max_x, max_y = self.board_boundary

            # 考虑组件尺寸
            half_width = component.width / 2
            half_height = component.height / 2

            component.position[0] = max(min_x + half_width,
                                      min(max_x - half_width, component.position[0]))
            component.position[1] = max(min_y + half_height,
                                      min(max_y - half_height, component.position[1]))

    def update_positions(self):
        """更新所有组件的位置"""
        # 清零所有力
        for comp in self.components:
            comp.net_force = np.array([0.0, 0.0])

        # 计算排斥力
        for i, comp1 in enumerate(self.components):
            for j, comp2 in enumerate(self.components):
                if i != j:
                    repulsion = self.calculate_repulsion_force(comp1, comp2)
                    comp1.net_force += repulsion

        # 更新速度和位置
        for comp in self.components:
            if not comp.is_fixed:
                # 更新速度：v = v + a*dt
                acceleration = comp.net_force  # 假设质量为1
                comp.velocity += acceleration * self.time_step

                # 应用阻尼
                comp.velocity *= self.damping

                # 更新位置：x = x + v*dt
                comp.position += comp.velocity * self.time_step

                # 应用边界约束
                self.apply_boundary_constraints(comp)

    def run_simulation(self, iterations=100, tolerance=0.01):
        """运行力导向布局仿真"""
        for iteration in range(iterations):
            self.update_positions()

            # 检查收敛性
            total_kinetic_energy = sum(np.linalg.norm(comp.velocity)**2
                                     for comp in self.components if not comp.is_fixed)

            if total_kinetic_energy < tolerance:
                print(f"Converged after {iteration + 1} iterations")
                break

        return [(comp.id, comp.position.tolist()) for comp in self.components]
