#ifndef GRAPH_H
#define GRAPH_H

#include <memory>
#include <string>
#include <vector>

namespace force_directed {

struct Node {
  enum class Type {
    kCircle,
    kRect
  };

  Type type;
  std::string id;
  double width;
  double height;

  std::pair<double,double> forces;
  std::pair<double,double> position;
  std::pair<double,double> velocity;
};

struct Edge {
  std::string id;
  std::shared_ptr<Node> source;
  std::shared_ptr<Node> target;
};

class Graph {
  using Nodes = std::vector<std::shared_ptr<Node>>;
  using Edges = std::vector<std::shared_ptr<Edge>>;

public:
  Nodes nodes();
  Edges edges();

  void set_nodes(const Nodes& nodes);
  void set_edges(const Edges& edges);
  void add_node(const std::shared_ptr<Node>& node);
  void add_edge(const std::shared_ptr<Edge>& edge);

private:
  Nodes nodes_;
  Edges edges_;
};

}  // namespace force_layout

#endif //GRAPH_H
