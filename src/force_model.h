#ifndef FORCELAYOUT_FORCE_MODEL_H
#define FORCELAYOUT_FORCE_MODEL_H
#include "graph.h"

namespace force_directed {

class ForceModel {
 public:
  virtual void Apply(Graph& graph) = 0;
  virtual double Energy() = 0;
  virtual ~ForceModel() = default;
};

// 斥力模型
class RepulsiveForce final : public ForceModel {
  void Apply(Graph& graph) override;
  double Energy() override;
};

// 引力模型
class AttractiveForce final : public ForceModel {
  void Apply(Graph& graph) override;
  double Energy() override;
};

// 边界力模型
class BoundaryRepulsiveForce final : public ForceModel {
  void Apply(Graph& graph) override;
  double Energy() override;
};

// 禁布区力模型
class KeepoutRepulsiveForceModule final : public ForceModel {
  void Apply(Graph& graph) override;
  double Energy() override;
};

// 拓扑关系保持力模型
class TopologyForceModule final : public ForceModel {
  void Apply(Graph& graph) override;
  double Energy() override;
};

}  // namespace force_layout

#endif  // FORCELAYOUT_FORCE_MODEL_H
