#include "force_directed.h"
#include <random>

namespace force_directed {





ForceDirected::ForceDirected(const Graph& graph) {
  graph_ = graph;
  temperature = config_.temperature;
}

void ForceDirected::Run() {
  for (int i = 0; i < config_.iterations; i++) {
    ApplyForces();

    UpdatePositions(0.1);

    if (i % 10 == 0) {
      double current_energy = Energy();
      // energy threshold
      if (std::abs(current_energy - last_energy_) < 1e-5) {
        printf("Converged at iteration %d, Energy: %f\n", i, current_energy);
        break;
      }
      last_energy_ = current_energy;

      if (i % 100 == 0) {
        printf("Iteration %d, Energy: %f\n", i, current_energy);
      }
    }
  }
}

void ForceDirected::ApplyForces() {
  for (const auto& force:force_models_) {
    force->Apply(graph_);
  }
}

// calculate new position
void ForceDirected::UpdatePositions(double step) {
  for (auto& node : graph_.nodes()) {
    node->forces.first *= std::min(1.0,temperature);
    node->forces.second *= std::min(1.0,temperature);

    // add random force,
    if (temperature > 0.3) {
      std::random_device rd;
      std::mt19937 generator(rd());
      std::normal_distribution<double> dist(0.0, temperature * 0.5);
      std::pair<double,double> random_force = {dist(generator), dist(generator)};
      node->forces.first += random_force.first;
      node->forces.second += random_force.second;
    }
  }
}

double ForceDirected::Energy() const {
  double sum = 0.0;
  for (const auto& force : force_models_) {
    sum += force->Energy();
  }
  return sum;
}

}  // namespace force_directed
